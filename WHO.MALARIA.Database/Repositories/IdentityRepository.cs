﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Dapper;
using WHO.MALARIA.Database.IRepositories;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Features;

namespace WHO.MALARIA.Database.Repositories
{
    public class IdentityRepository : Repository<Identity>, IIdentityRepository
    {
        private readonly IDbManager _dbManager;

        public IdentityRepository(IDbManager dbManager, MalariaDbContext dbContext) : base(dbContext)
        {
            _dbManager = dbManager;
        }

        #region Entity Framework Methods

        public Identity Get(Guid id)
        {
            throw new NotImplementedException();
        }

        public Identity Get(FilterCriteria filterCriteria)
        {
            throw new NotImplementedException();
        }

        public IEnumerable<Identity> GetAll()
        {
            throw new NotImplementedException();
        }

        public IEnumerable<Identity> GetAll(List<FilterCriteria> filterCriterias)
        {
            throw new NotImplementedException();
        }
        #endregion

        #region Dapper Methods
        /// <summary>
        /// Retrieve all the identity records and associated user
        /// </summary>
        /// <returns>IEnumerable IdentityDto object</returns>
        public async Task<IEnumerable<IdentityDto>> GetAllIdentities()
        {
            // creating a sql statement with join on User table
            string sql = $"SELECT I.Id , I.Email, I.Username, I.Mode, I.Locked, I.LockoutEnd, I.AccessFailedCount, I.LastLoginOn, I.IsDeactivated, " +
                "I.ChangePassword, I.PasswordChangedOn, I.ShouldChangePassword, I.Language, I.Theme, I.TwoFactorEnabled, " +
                "I.AuthenticatorKey, I.RecoveryCodes, I.UpdatePasswordLinkValidUntil, I.UpdatePasswordLink, I.Status, " +
                $"U.Id, {MalariaSchemas.Internal}.GetUserType(U.Id) AS UserType, U.Status as UserStatus " +
                $"FROM [{MalariaSchemas.Internal}].[Identity] I " +
                $"INNER JOIN [{MalariaSchemas.Internal}].[User] U ON I.Id = U.IdentityId ";

            var identities = await _dbManager.QueryAsync<IdentityDto, UserDto, IdentityDto>(sql,
                (identity, user) => { identity.User = user; identity.User.IdentityId = identity.Id; return identity; }, splitOn: "Id");

            return identities;
        }

        /// <summary>
        /// Retrieve all the identities records and assosicated user with that identity
        /// </summary>
        /// <param name="filterCriterias">A filter criteria to perform filter</param>
        /// <returns>IEnumerable object of IdentityDto</returns>
        public async Task<IEnumerable<IdentityDto>> GetAllIdentitiesWithFilterCriteria(List<FilterCriteria> filterCriterias)
        {
            string whereClause = string.Empty;

            whereClause = CommandGenerator.BuildWhereClauseParameters(filterCriterias);

            // creating a sql statement with join on User table
            string sql = $"SELECT I.Id , I.Email, I.Username, I.Mode, I.Locked, I.LockoutEnd, I.AccessFailedCount, I.LastLoginOn, I.IsDeactivated, " +
                "I.ChangePassword, I.PasswordChangedOn, I.ShouldChangePassword, I.Language, I.Theme, I.TwoFactorEnabled, " +
                "I.AuthenticatorKey, I.RecoveryCodes, I.UpdatePasswordLinkValidUntil, I.UpdatePasswordLink, I.Status,I.PasswordHash, " +
                $"U.Id, {MalariaSchemas.Internal}.GetUserType(U.Id) AS UserType, U.Status as UserStatus " +
                $"FROM [{MalariaSchemas.Internal}].[Identity] I " +
                $"INNER JOIN [{MalariaSchemas.Internal}].[User] U ON I.Id = U.IdentityId ";


            if (!string.IsNullOrWhiteSpace(whereClause))
            {
                sql = string.Concat(sql, " ", "WHERE", " ", whereClause);
            }

            var dynamicParameters = new DynamicParameters();

            filterCriterias?.ForEach(filter =>
            {
                dynamicParameters.Add($"@{filter.Field}", filter.Value);
            });

            var identities = await _dbManager.QueryAsync<IdentityDto, UserDto, IdentityDto>(sql,
                (identity, user) => { identity.User = user; identity.User.IdentityId = identity.Id; return identity; }, dynamicParameters, splitOn: "Id");

            return identities;
        }

        /// <summary>
        /// Get a Sigle Identity object with identity Id along with the ass
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public async Task<IdentityDto> GetIdentityById(Guid id)
        {
            if (id == Guid.Empty)
            {
                throw new ArgumentNullException("parameter id is null or empty");
            }

            // creating a sql statement with join on User table
            string sql = $"SELECT I.Id , I.Email, I.Username, I.Mode, I.Locked, I.LockoutEnd, I.AccessFailedCount, I.LastLoginOn, I.IsDeactivated, " +
                "I.ChangePassword, I.PasswordChangedOn, I.ShouldChangePassword, I.Language, I.Theme, I.TwoFactorEnabled, " +
                "I.AuthenticatorKey, I.RecoveryCodes, I.UpdatePasswordLinkValidUntil, I.UpdatePasswordLink, I.Status,I.PasswordHash, " +
                $"U.Id, {MalariaSchemas.Internal}.GetUserType(U.Id) AS UserType, U.Status as UserStatus " +
                $"FROM [{MalariaSchemas.Internal}].[Identity] I " +
                $"INNER JOIN [{MalariaSchemas.Internal}].[User] U ON I.Id = U.IdentityId WHERE I.Id = @Id";

            var identity = await _dbManager.QuerySingleAsync<IdentityDto>(sql, new { @Id = id });


            return identity;

        }
        #endregion
    }
}
