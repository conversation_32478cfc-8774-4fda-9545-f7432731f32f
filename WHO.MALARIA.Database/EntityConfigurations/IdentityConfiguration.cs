﻿using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using WHO.MALARIA.Domain.Models.Identity;

namespace WHO.MALARIA.Database.EntityConfigurations
{
    public class IdentityConfiguration : IEntityTypeConfiguration<Identity>
    {

        public void Configure(EntityTypeBuilder<Identity> builder)
        {
            builder.ToTable("Identity", MalariaSchemas.Internal);

            builder.<PERSON><PERSON><PERSON>(b => b.Id);

            builder.Property(b => b.Id)
                .HasColumnName("Id")
                .HasColumnType("UNIQUEIDENTIFIER");

            builder.Property(b => b.Username)
                    .HasColumnName("Username")
                    .HasColumnType("NVARCHAR(255)")
                    .IsRequired();

            builder.Property(b => b.Email)
                .HasColumnName("Email")
                .HasColumnType("NVARCHAR(255)");

            builder.Property(b => b.Mode)
                .HasColumnName("Mode")
                .HasColumnType("NVARCHAR(20)")
                .IsRequired();

            builder.Property(b => b.PasswordHash)
                .HasColumnName("PasswordHash")
                .HasColumnType("NVARCHAR(255)");

            builder.Property(b => b.Locked)
                .HasColumnName("Locked")
                .HasColumnType("BIT")
                .HasDefaultValue(false)
                .IsRequired();

            builder.Property(b => b.LockoutEnd)
                .HasColumnName("LockoutEnd")
                .IsRequired(false);

            builder.Property(b => b.AccessFailedCount)
                .HasColumnName("AccessFailedCount")
                .HasColumnType("int")
                .IsRequired();

            builder.Property(b => b.LastLoginOn)
                .HasColumnName("LastLoginOn")
                .IsRequired(false);

            builder.Property(b => b.ChangePassword)
                .HasColumnName("ChangePassword")
                .HasColumnType("BIT")
                .IsRequired()
                .HasDefaultValue(false);

            builder.Property(b => b.PasswordChangedOn)
                .HasColumnName("PasswordChangedOn")
                .HasColumnType("DATETIME")
                .IsRequired(false);

            builder.Property(b => b.ShouldChangePassword)
                .HasColumnName("ShouldChangePassword")
                .HasColumnType("BIT")
                .HasDefaultValue(false)
                .IsRequired();

            builder.Property(b => b.Language)
                .HasColumnName("Language")
                .HasColumnType("CHAR")
                .HasMaxLength(5);

            builder.Property(b => b.Theme)
                .HasColumnName("Theme")
                .HasColumnType("NVARCHAR(255)");

            builder.Property(b => b.TwoFactorEnabled)
                .HasColumnName("TwoFactorEnabled")
                .HasColumnType("BIT")
                .HasDefaultValue(false);

            builder.Property(b => b.AuthenticatorKey)
                .HasColumnName("AuthenticatorKey")
                .HasColumnType("NVARCHAR(50)");

            builder.Property(b => b.RecoveryCodes)
                .HasColumnName("RecoveryCodes")
                .HasColumnType("VARCHAR(100)");

            builder.Property(b => b.UpdatePasswordLinkValidUntil)
                .HasColumnName("UpdatePasswordLinkValidUntil")
                .HasColumnType("DATETIME")
                .IsRequired(false);

            builder.Property(b => b.UpdatePasswordLink)
                .HasColumnName("UpdatePasswordLink")
                .HasColumnType("VARCHAR(400)");

            builder.Property(b => b.Status)
                .HasColumnName("Status")
                .HasColumnType("BIT")
                .HasDefaultValue(0);

            builder.Property(b => b.IsDeactivated)
                .HasColumnName("IsDeactivated")
                .HasColumnType("BIT")
                .HasDefaultValue(0);

            builder.HasIndex(x => x.Email).IsUnique();
        }
    }
}
