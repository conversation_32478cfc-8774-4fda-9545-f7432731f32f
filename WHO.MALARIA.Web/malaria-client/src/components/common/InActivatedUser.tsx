import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { authService } from "../../services/authService";
import InactiveUserProfile from "../common/InactiveUserProfile";
import Modal from "../controls/Modal";

/** Renders for in active user */
function InActivatedUser() {
    const { t } = useTranslation();
    document.title = t("app.InactivatedUser");
    const [openNewUserDialog, setOpenNewUserDialog] = useState<boolean>(false);

    // on back button click redirect user to landing screen
    const onNavigate = () => {
        authService.logout();
    };

    return (
        <div className="section-background-image" style={{ height: '100vh' }}>

            <div className="section-bg-content">

                <div className="content">{t("Errors.InActivatedUserMessage")}</div>

                <div className="button-group">
                    <button className={"btn app-btn-secondary"} onClick={() => setOpenNewUserDialog(true)}>
                        {t("Common.Yes")}
                    </button>
                    <button className={"btn app-btn-secondary mx-2"} onClick={onNavigate}>
                        {t("Common.No")}
                    </button>                    
                </div>
                {
                    <Modal
                        open={openNewUserDialog}
                        title={t("translation:UserManagement.Profile")}
                        onEscPress={false}
                        onDialogClose={() => setOpenNewUserDialog(false)}
                    >
                        <InactiveUserProfile onCancel={() => setOpenNewUserDialog(false)} />
                    </Modal>
                }                
            </div>
        </div>
    );
}

export default InActivatedUser;