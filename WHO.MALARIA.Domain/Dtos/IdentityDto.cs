﻿using System;
using Newtonsoft.Json;

namespace WHO.MALARIA.Domain.Dtos
{
    public class IdentityDto
    {
        [JsonProperty("id")]
        public Guid Id { get; set; }

        [<PERSON>sonProperty("username")]
        public string Username { get; set; }
        [JsonProperty("email")]

        public string Name { get; set; }
        [JsonProperty("name")]

        public string Email { get; set; }
        [JsonProperty("mode")]
        public string Mode { get; set; }

        [JsonProperty("locked")]
        public bool Locked { get; set; }

        [<PERSON><PERSON><PERSON>roperty("passwordChangedOn")]
        public DateTime? PasswordChangedOn { get; set; }

        [Json<PERSON>roperty("shouldChangePassword")]
        public bool ShouldChangePassword { get; set; }

        [JsonProperty("language")]
        public string Language { get; set; }

        [JsonProperty("theme")]
        public string Theme { get; set; }

        [Json<PERSON>roperty("twoFactorEnabled")]
        public bool TwoFactorEnabled { get; set; }

        [Json<PERSON>roperty("authenticatorKey")]
        public string AuthenticatorKey { get; set; }

        [JsonProperty("recoveryCodes")]
        public string RecoveryCodes { get; set; }

        [JsonProperty("updatePasswordLinkValidUntil")]
        public DateTime? UpdatePasswordLinkValidUntil { get; set; }

        [JsonProperty("updatePasswordLink")]
        public string UpdatePasswordLink { get; set; }

        [JsonProperty("status")]
        public bool Status { get; set; }

        [JsonProperty("accessFailedCount")]
        public int AccessFailedCount { get; set; }

        [JsonProperty("passwordHash")]
        public string PasswordHash { get; set; }

        [JsonProperty("user")]
        public UserDto User { get; set; }

        [JsonProperty("requestedCountryIds")]
        public Guid[] RequestedCountryIds { get; set; }

        [JsonProperty("isDeactivated")]
        public bool IsDeactivated { get; set; }

    }
}
