﻿using System;
namespace WHO.MALARIA.Domain.Models.Identity
{
    public class Identity : ModelBase
    {
        public Guid Id { get; set; }
        public string Username { get; set; }
        public string Email { get; set; }
        public string Mode { get; set; }
        public string PasswordHash { get; set; }
        public bool Locked { get; set; }
        public DateTime? LockoutEnd { get; set; }
        public int AccessFailedCount { get; set; }
        public DateTime? LastLoginOn { get; set; }
        public bool ChangePassword { get; set; }
        public DateTime? PasswordChangedOn { get; set; }
        public bool ShouldChangePassword { get; set; }
        public string Language { get; set; }
        public string Theme { get; set; }
        public bool TwoFactorEnabled { get; set; }
        public string AuthenticatorKey { get; set; }
        public string RecoveryCodes { get; set; }
        public DateTime? UpdatePasswordLinkValidUntil { get; set; }
        public string UpdatePasswordLink { get; set; }
        public bool Status { get; set; }
        public bool IsDeactivated { get; set; }
        public User User { get; set; }

        public Identity(string username, string email, string mode, bool status)
        {
            this.Id = Guid.NewGuid();
            this.Username = username;
            this.Email = email;
            this.Mode = mode;
            this.Status = status;
        }
    }

}
